import React from 'react';
import { Form, Formik } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import { Button, Grid } from '@mui/material';
import { Alert } from 'react-bootstrap';
import { addCaisseRequest, updateCaisseRequest } from './actions';
import { CustomTextInput } from 'containers/Common/CustomInputs/CustomTextInput';
import * as Yup from 'yup';
import { createStructuredSelector } from 'reselect';
import ChooseDonor from 'containers/Common/SubComponents/ChooseDonor';
import { makeSelectLoading, makeSelectError } from './selectors';



const validationSchema = Yup.object({
    donor: Yup.object().required('Le donateur est obligatoire'),
    amount: Yup.number().required('Le montant est obligatoire'),
    dateEmprunt: Yup.date().required('La date d\'emprunt est obligatoire'),
    description: Yup.string(),
});

const stateSelector = createStructuredSelector({
    loading: makeSelectLoading,
    error: makeSelectError,
});

export default function CommunCaisseForm({ initialValues, onSubmit, isUpdate = false }) {
    const dispatch = useDispatch();
    const { loading, error } = useSelector(stateSelector);

    return (
        <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            enableReinitialize
            onSubmit={values => {
                if (isUpdate) {
                    dispatch(updateCaisseRequest(values));
                } else {
                    dispatch(addCaisseRequest(values));
                }
                if (onSubmit) onSubmit();
            }}
        >
            {formikProps => (
                <div style={{ background: '#fff', padding: '2rem', borderRadius: '12px' }}>
                    {error && (
                        <Alert variant="danger" style={{ marginBottom: '1rem' }}>
                            {(error.response && error.response.data && error.response.data.detailedMessage) || error.message || 'Une erreur est survenue'}
                        </Alert>
                    )}
                    <Form onSubmit={formikProps.handleSubmit}>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={12}>
                                <div style={{ width: '50%', margin: '0 auto' }}>
                                    <ChooseDonor props={formikProps} />
                                </div>
                            </Grid>

                            <Grid item xs={12} sm={12}>
                                <CustomTextInput name="amount" label="Montant *" type="number" formProps={formikProps} />
                            </Grid>
                            <Grid item xs={12} sm={12}>
                                <CustomTextInput name="dateEmprunt" label="Date Emprunt *" type="date" formProps={formikProps} />
                            </Grid> 
                            <Grid item xs={12} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                                <Button type="submit" variant="contained" color="primary" disabled={loading}>
                                    {loading ? 'En cours...' : (isUpdate ? "Modifier la caisse" : "Ajouter la caisse")}
                                </Button>
                            </Grid>
                        </Grid>
                    </Form>
                </div>
            )}
        </Formik>
    );
}
