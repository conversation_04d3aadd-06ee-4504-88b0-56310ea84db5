package ma.almobadara.backend.controller.migration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.service.migration.BeneficiaryActifFamilyMigrationService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
@RequestMapping("/beneficiary-actif-family-migration")
@Slf4j
@RequiredArgsConstructor
public class BeneficiaryActifFamilyMigrationController {

    private final BeneficiaryActifFamilyMigrationService beneficiaryActifFamilyMigrationService;

    @PostMapping("/upload")
    public ResponseEntity<String> migrateBeneficiaryActifFamily(@RequestParam("file") MultipartFile file,
                                                                @RequestParam("zoneId") Long zoneId,
                                                                @RequestParam("cityId") Long cityId,
                                                                @RequestParam(value = "beneficiaryCategoryId", required = false) Long beneficiaryCategoryId,
                                                                @RequestParam("beneficiaryStatusId") Long beneficiaryStatusId) throws FunctionalException {
        log.info("Received request to migrate beneficiary actif family data");
        try {
            beneficiaryActifFamilyMigrationService.migrateBeneficiaryFamily(file, zoneId, cityId, beneficiaryCategoryId, beneficiaryStatusId);
            return ResponseEntity.ok("Beneficiary actif family data migration completed successfully.");
        } catch (IOException e) {
            log.error("Error during migration: {}", e.getMessage());
            return ResponseEntity.internalServerError().body("Error during migration: " + e.getMessage());
        }
    }

    @PostMapping("/upload-education")
    public ResponseEntity<String> migrateEducationBeneficiary(@RequestParam("file") MultipartFile file) throws FunctionalException {
        log.info("Received request to migrate beneficiary actif family data");
        try {
            beneficiaryActifFamilyMigrationService.migrateBeneficiaryEducation(file);
            return ResponseEntity.ok("Beneficiary actif family data migration completed successfully.");
        } catch (IOException e) {
            log.error("Error during migration: {}", e.getMessage());
            return ResponseEntity.internalServerError().body("Error during migration: " + e.getMessage());
        }
    }
} 