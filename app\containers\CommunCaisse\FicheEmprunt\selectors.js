import { createSelector } from 'reselect';
import { initialState } from './reducer';

const selectOmdb = state => state.ficheEmprunt || initialState;

const makeSelectEmpruntDetails = createSelector(
  selectOmdb,
  omdbState => omdbState.empruntDetails,
);

const makeSelectLoading = createSelector(
  selectOmdb,
  omdbState => omdbState.loading,
);

const makeSelectError = createSelector(
  selectOmdb,
  omdbState => omdbState.error,
);

const makeSelectDeleteSuccess = createSelector(
  selectOmdb,
  omdbState => omdbState.deleteSuccess,
);

export {
  selectOmdb,
  makeSelectLoading,
  makeSelectError,
  makeSelectEmpruntDetails,
  makeSelectDeleteSuccess,
};
