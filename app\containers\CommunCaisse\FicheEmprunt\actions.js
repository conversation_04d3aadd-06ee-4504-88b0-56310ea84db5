import {
  LOAD_EMPRUNT_DETAILS,
  LOAD_EMPRUNT_DETAILS_SUCCESS,
  LOAD_EMPRUNT_DETAILS_ERROR,
  DELETE_EMPRUNT,
  DELETE_EMPRUNT_SUCCESS,
  DELETE_EMPRUNT_ERROR,
  RESET_DELETE_EMPRUNT,
  RESET_ERROR,
} from './constants';

export function loadEmpruntDetails(payload) {
  return {
    type: LOAD_EMPRUNT_DETAILS,
    payload,
  };
}

export function loadEmpruntDetailsSuccess(empruntDetails) {
  return {
    type: LOAD_EMPRUNT_DETAILS_SUCCESS,
    empruntDetails,
  };
}

export function loadEmpruntDetailsError(error) {
  return {
    type: LOAD_EMPRUNT_DETAILS_ERROR,
    error,
  };
}

export function deleteEmprunt(id) {
  return {
    type: DELETE_EMPRUNT,
    id,
  };
}

export function deleteEmpruntSuccess() {
  return {
    type: DELETE_EMPRUNT_SUCCESS,
  };
}

export function deleteEmpruntError(error) {
  return {
    type: DELETE_EMPRUNT_ERROR,
    error,
  };
}

export function resetDeleteEmprunt() {
  return {
    type: RESET_DELETE_EMPRUNT,
  };
}

export function resetError() {
  return {
    type: RESET_ERROR,
  };
}
