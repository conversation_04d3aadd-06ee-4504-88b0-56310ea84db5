import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request';
import {
  loadEmpruntDetailsSuccess,
  loadEmpruntDetailsError,
  deleteEmpruntSuccess,
  deleteEmpruntError,
} from './actions';
import {
  LOAD_EMPRUNT_DETAILS,
  DELETE_EMPRUNT,
} from './constants';

function* fetchEmpruntDetails(action) {
  const url = `/caisses-emprunt/${action.payload.id}`;
  try {
    const { data } = yield call(request.get, url);
    yield put(loadEmpruntDetailsSuccess(data));
  } catch (error) {
    yield put(loadEmpruntDetailsError(error));
  }
}

function* deleteEmprunt(action) {
  const url = `/caisses-emprunt/${action.id}`;
  try {
    yield call(request.delete, url);
    yield put(deleteEmpruntSuccess());
  } catch (error) {
    yield put(deleteEmpruntError(error));
  }
}

export default function* detailEmpruntSaga() {
  yield takeLatest(LOAD_EMPRUNT_DETAILS, fetchEmpruntDetails);
  yield takeLatest(DELETE_EMPRUNT, deleteEmprunt);
}
