import React from 'react';
import { Link, NavLink, useParams } from 'react-router-dom';

import styles from '../../../../Css/profileHeader.css';

const HeaderEmprunt = props => {
  const params = useParams();
  return (
    <div className={styles.navBar}>
      <p>
        <NavLink
          exact
          to={`/commun-caisse/fiche/${params.id}/historique`}
          activeClassName={styles.selected}
        >
          Historique
        </NavLink>
      </p>
    </div>
  );
};

export default HeaderEmprunt;
