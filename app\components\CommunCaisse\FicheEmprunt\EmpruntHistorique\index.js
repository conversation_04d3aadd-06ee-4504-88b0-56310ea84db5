import React, { useState } from 'react';
import DataTable from 'components/Common/DataTable';
import CustomPagination from 'components/Common/CustomPagination';
import styles from '../../../../Css/profileList.css';
import stylesList from '../../../../Css/profileList.css';

function EmpruntHistorique({ data }) {
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  const emprunt = data;
  let listHistoryEmprunt = [];

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
  };

  const formatAmount = (amount) => {
    if (!amount && amount !== 0) return '-';
    return `${amount.toLocaleString('fr-FR')} DH`;
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'EMPRUNT':
        return 'Emprunt';
      case 'REMBOURSEMENT':
        return 'Remboursement';
      default:
        return type || '-';
    }
  };



  if (emprunt && emprunt.histories) {
    const historySorted = [...emprunt.histories].sort((a, b) =>
      new Date(b.dateEmprunt || b.dateRemboursement) - new Date(a.dateEmprunt || a.dateRemboursement)
    );

    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, historySorted.length);
    const paginatedHistory = historySorted.slice(startIndex, endIndex);

    listHistoryEmprunt = paginatedHistory.map((history, index) => ({
      id: history.id || index,
      dateTransaction: history.dateEmprunt || history.dateRemboursement,
      amount: history.amount,
      type: history.type,
    }));
  }

  const columns = [
    {
      field: 'dateTransaction',
      headerName: 'Date',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => formatDate(params.value),
    },
    {
      field: 'type',
      headerName: 'Type',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <span style={{
          color: params.value === 'EMPRUNT' ? '#d32f2f' : '#2e7d32',
          fontWeight: 'bold',
          padding: '4px 8px',
          borderRadius: '4px',
          backgroundColor: params.value === 'EMPRUNT' ? '#ffebee' : '#e8f5e8'
        }}>
          {getTypeLabel(params.value)}
        </span>
      ),
    },
    {
      field: 'amount',
      headerName: 'Montant',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <span style={{ fontWeight: 'bold' }}>
          {formatAmount(params.value)}
        </span>
      ),
    },
  ];

  const empruntCode = emprunt ? emprunt.donorName : '';

  return (
    <div>
      <div className={`pb-5 ${stylesList.backgroudStyle}`}>
        <div>
          <div className={styles.global}>
            <div className={styles.header}>
              <h4>Historique</h4>
            </div>
            <DataTable
              rows={listHistoryEmprunt}
              columns={columns}
              fileName={`Historique des transactions de l'emprunt ${empruntCode}, ${new Date().toLocaleString()}`}
            />
            <div className="justify-content-center my-4">
              {emprunt && emprunt.histories && emprunt.histories.length > 0 && (
                <CustomPagination
                  totalElements={emprunt.histories.length}
                  totalCount={Math.ceil(emprunt.histories.length / pageSize)}
                  pageSize={pageSize}
                  currentPage={currentPage}
                  onPageChange={setCurrentPage}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default EmpruntHistorique;
