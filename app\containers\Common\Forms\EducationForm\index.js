import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { Switch } from 'formik-material-ui';
import { Field, Form, Formik } from 'formik';

import { CustomTextInput } from 'containers/Common/CustomInputs/CustomTextInput';
import btnStyles from 'Css/button.css';
import styles from 'Css/form.css';
import { Alert } from 'react-bootstrap';
import SchoolLevels from 'containers/Common/SubComponents/SchoolLevels';
import SchoolYears from 'containers/Common/SubComponents/SchoolYears';
import CountryRegionCity from 'containers/Common/SubComponents/CountryRegionCity';
import Honors from 'containers/Common/SubComponents/Honors';
import EducationSystemType from 'containers/Common/SubComponents/EducationSystemType';
import { CustomTextInputAr } from 'containers/Common/CustomInputs/CustomTextInputAr';
import Majors from 'containers/Common/SubComponents/Majors';
import { CustomSelect } from 'containers/Common/CustomInputs/CustomSelect';
import { makeSelectSchoolLevels } from 'containers/Common/SubComponents/SchoolLevels/selectors';
import { loadBeneficiary } from 'containers/Beneficiary/BeneficiaryProfile/actions';
import { CustomTextArea } from 'containers/Common/CustomInputs/CustomTextArea';
import {
  validationSchemaPrimaire,
  validationSchemaSecondaire,
  validationSchemaPrescolaire,
  validationSchemaNiveauActuell,
} from './validationSchema';
import {
  addEducation,
  resetEducation,
  updateEducationCurrent,
  updateEducationCurrentReset,
} from './actions';
import {
  makeSelectEducationsAddBeneficiarySuccess,
  makeSelectError,
  makeSelectSuccess,
  makeSelectUpdateEducationCurrent,
  makeSelectUpdateEducationError,
  makeSelectUpdateEducationSuccess,
} from './selectors';
import { initialValues } from './initialValues';

const omdbSelector = createStructuredSelector({
  success: makeSelectSuccess,
  educationBeneficiaryAddSuccess: makeSelectEducationsAddBeneficiarySuccess,
  error: makeSelectError,
  schoolLevels: makeSelectSchoolLevels,
  updateCurrentEducation: makeSelectUpdateEducationCurrent,
  udpatEducationCurrentSuccess: makeSelectUpdateEducationSuccess,
  updateCurrentEducationError: makeSelectUpdateEducationError,
});

const backgroundStyle = {
  backgroundColor: 'white',
  border: '2px solid white ',
  borderRadius: '10px',
};

export default function EducationForm(props) {
  const formikRef = useRef();
  const [showAlert, setShowAlert] = useState(false);
  const dispatch = useDispatch();
  const currentEditableRow = props.beneficiaryCurrentEducation
    ? props.beneficiaryCurrentEducation
    : null;
  const isEdit = Boolean(props.isEditModal);
  const [selectedSchoolLevel, setSelectedSchoolLevel] = useState('');

  let myAlert = null;
  const { handleClose } = props;
  useEffect(() => {
    if (isEdit && currentEditableRow) {
      setSelectedSchoolLevel(currentEditableRow.person.schoolLevel.type);
    }
  }, [currentEditableRow, isEdit]);
  let educationType = '';
  let majorLabel = '';
  const buttonText = isEdit ? 'Modifier' : 'Ajouter';

  const {
    educationBeneficiaryAddSuccess,
    error,
    schoolLevels,
    updateCurrentEducation,
    updateCurrentEducationError,
    udpatEducationCurrentSuccess,
  } = useSelector(omdbSelector);

  if (props) {
    educationType = props.educationType ? props.educationType : '';
    if (educationType == 'secondaire') {
      majorLabel = 'Option';
    } else {
      majorLabel = 'Filière';
    }
  }

  const SelectMajorHandler = value => {
    formikRef.current.setFieldValue(`major.name`, value.name);
  };
  const SelectSchoolLevelHandler = value => {
    formikRef.current.setFieldValue(`schoolLevel.name`, value.name);
  };
  const SelectSchoolYearHandler = value => {
    formikRef.current.setFieldValue(`schoolYear.name`, value.name);
  };
  const SelectCityHandler = value => {
    formikRef.current.setFieldValue(`city.name`, value.name);
  };
  const SelectHonorHandler = value => {
    formikRef.current.setFieldValue(`honor.name`, value.name);
  };

  const SelectEducationSystemTypeHandler = value => {
    formikRef.current.setFieldValue(`educationSystemType.name`, value.name);
  };

  useEffect(() => {
    if (educationBeneficiaryAddSuccess) {
      formikRef.current.resetForm();
      props.handleClose();
    }
  }, [educationBeneficiaryAddSuccess]);

  useEffect(() => {
    if (udpatEducationCurrentSuccess) {
      props.handleClose();
      dispatch(loadBeneficiary(currentEditableRow.id));
      dispatch(updateEducationCurrentReset());
    }
  }, [udpatEducationCurrentSuccess]);

  useEffect(() => {
    if (error) {
      setShowAlert(true);
    }
  }, [error]);

  useEffect(() => {
    if (props.show == false) {
      formikRef.current.resetForm();
      if (error) {
        dispatch(resetEducation());
      }
    }
  }, [props.show]);

  if (showAlert) {
    myAlert = (
      <Alert
        className="pb-0"
        variant="danger"
        onClose={() => setShowAlert(false)}
        dismissible
      >
        <p>Erreur lors de l'ajout de cette education !</p>
      </Alert>
    );
  }

  useEffect(() => {
    if (props.education && formikRef.current) {
      const temp = { ...props.education };
      const educationInfo = props.education.info;
      let info = null;

      if (educationInfo) {
        info = {
          country: {
            id: educationInfo.country ? educationInfo.country.id : '',
          },
          region: {
            id: educationInfo.region ? educationInfo.region.id : '',
          },
          info: {
            id: educationInfo.id,
          },
        };
      }
      console.log(temp)
      const editValues = {
        ...temp,
        ...info,
        regions: formikRef.current.values.regions,
        cities: formikRef.current.values.cities,
        // Set semestre if exists in edit mode
        semestre: temp.semestre.toLowerCase() || '',
      };

      formikRef.current.setValues(editValues);
    }
  }, [formikRef.current]);

  const saveRegions = data => {
    formikRef.current.setFieldValue('regions', data);
  };
  const saveCities = data => {
    formikRef.current.setFieldValue('cities', data);
  };
  const resetCity = field => {
    formikRef.current.setFieldValue('cities', {});
  };

  const [schoolLevelTypes, setSchoolLevelTypes] = useState([]);

  useEffect(() => {
    if (schoolLevels) {
      setSchoolLevelTypes(extractUniqueTypes(schoolLevels));
    }
  }, [schoolLevels]);
  const extractUniqueTypes = data => {
    const types = [...new Set(data.map(item => item.type))];
    return types.map(type => ({
      value: type,
      label: type.charAt(0).toUpperCase() + type.slice(1),
    }));
  };

  const validatedSchema = useMemo(() => {
    let chosenValidation = null;
    educationType == 'primaire'
      ? (chosenValidation = validationSchemaPrimaire)
      : educationType == 'préscolaire'
        ? (chosenValidation = validationSchemaPrescolaire)
        : educationType == 'actuelle'
          ? (chosenValidation = validationSchemaNiveauActuell)
          : (chosenValidation = validationSchemaSecondaire);

    return chosenValidation;
  }, [educationType]);
  const initialValuesAcutal = useMemo(() => {
    if (currentEditableRow) {
      return {
        schoolLevel: {
          id: currentEditableRow.person.schoolLevel.id,
        },
        schoolName: currentEditableRow.person.schoolName,
        schoolNameAr: currentEditableRow.person.schoolNameAr,
        schoolLevelType: currentEditableRow.person.schoolLevel.type,
      };
    }
    return {
      schoolLevel: {
        id: '',
      },
      schoolName: '',
      schoolNameAr: '',
      schoolLevelType: '',
    };
  }, [currentEditableRow]);

  // onchange if teh school level is changed the the school level id should be setr to null to choose    new school level
  const handleSchoolLevelChange = e => {
    setSelectedSchoolLevel(e.target.value);
    formikRef.current.setFieldValue('schoolLevel.id', '');
    formikRef.current.setFieldValue('schoolLevelType', e.target.value);
  };

  return (
    <div className style={backgroundStyle}>
      {myAlert}

      {updateCurrentEducationError && (
        <Alert className="pb-0" variant="danger" dismissible>
          <p>Erreur lors de modification de cette education !</p>
        </Alert>
      )}
      <Formik
        key={educationType === 'actuelle' ? 'CURRENTKEY' : 'ELSEKEY'}
        initialValues={
          educationType === 'actuelle' ? initialValuesAcutal : initialValues
        }
        enableReinitialize
        validationSchema={validatedSchema}
        innerRef={formikRef}
        onSubmit={(values, { setSubmitting, resetForm }) => {
          if (educationType === 'actuelle') {
            dispatch(
              updateEducationCurrent({
                beneficiaryId: currentEditableRow.id,
                schoolLevelId: values.schoolLevel.id,
                schoolName: values.schoolName,
                schoolNameAr: values.schoolNameAr,
                studentNumber: values.studentNumber,
                educated: true,
                educations: currentEditableRow.educations,
                scholarshipBeneficiaries:
                  currentEditableRow.scholarshipBeneficiaries,
              }),
            );
            setSubmitting(false);
          } else if (props.beneficiaryId) {
            const city = {
              id: values.city.id,
              name: values.city.name,
              region: {
                id: values.region.id,
                country: {
                  id: values.country.id,
                },
              },
            };

            // Ensure notes is an array
            const notes = Array.isArray(values.notes)
              ? values.notes.filter(note => note !== '' && note.content !== '')
              : [];

            const body = {
              ...values,
              educationType,
              city,
              beneficiary: {
                id: props.beneficiaryId,
              },
              notes,
            };

            if (educationType == 'primaire') delete body.major;
            dispatch(addEducation(body, props.beneficiaryId));
            setSubmitting(false);
          } else {
            alert('une erreur est survenue');
          }
        }}
      >
        {({ handleSubmit, isSubmitting, ...props }) => (
          <Form className="mt-1" onSubmit={handleSubmit}>
            {educationType == 'actuelle' ? (
              <div className={`pt-2 mb-2 ${styles.miniForm}`}>
                <div className="pr-3 pb-3 pl-3 pt-0">
                  <div className="form-row">
                    <div className="form-group col-md-6">
                      <CustomSelect
                        label="Niveau scolaire"
                        name="schoolLevelType"
                        isRequired
                        onChange={handleSchoolLevelChange}
                      >
                        <option value="">-- Niveau scolaire --</option>
                        {schoolLevelTypes &&
                          schoolLevelTypes.map(type => (
                            <option key={type.value} value={type.value}>
                              {type.label}
                            </option>
                          ))}
                      </CustomSelect>
                    </div>
                    <div className="form-group col-md-6">
                      <SchoolLevels
                        isRequired
                        label="Class"
                        name="schoolLevel.id"
                        educationType={props.values.schoolLevelType}
                        isMeta
                        unique
                      />
                    </div>
                    <div className="form-group col-md-6">
                      <CustomTextInput
                        label="Etablissement"
                        name="schoolName"
                        formProps={props}
                        placeholder="Etablissement"
                      />
                    </div>
                    <div className="form-group col-md-6">
                      <CustomTextInputAr
                        label=" المؤسسة"
                        placeholder="المؤسسة"
                        name="schoolNameAr"
                        formikRef={formikRef}
                      />
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className={`pt-2 mb-2 ${styles.miniForm}`}>
                <div className="pr-3 pb-3 pl-3 pt-0">
                  <div className="form-row">
                    <div className="form-group col-md-6">
                      <CustomTextInput
                        label="Etablissement"
                        name="schoolName"
                        isRequired
                        formProps={props}
                        placeholder="Etablissement"
                      />
                    </div>
                    <div className="form-group col-md-6">
                      <CustomTextInputAr
                        label=" المؤسسة"
                        placeholder="المؤسسة"
                        name="schoolNameAr"
                        isRequired
                        formikRef={formikRef}
                      />
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="col-md-6">
                      <SchoolLevels
                        onSelectSchoolLevel={SelectSchoolLevelHandler}
                        label="Niveau scolaire"
                        name="schoolLevel.id"
                        educationType={educationType}
                        isMeta
                        isRequired
                        unique
                      ></SchoolLevels>
                    </div>
                    <div className="col-md-6">
                      <SchoolYears
                        onSelectSchoolYear={SelectSchoolYearHandler}
                        label="Année scolaire"
                        name="schoolYear.id"
                        isMeta
                        isRequired
                        unique
                      ></SchoolYears>
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group col-md-6">
                      <EducationSystemType
                        onSelectEducationSystemType={
                          SelectEducationSystemTypeHandler
                        }
                        label="Système éducatif"
                        name="educationSystemType.id"
                        isRequired
                        isMeta
                        unique
                      ></EducationSystemType>
                    </div>
                    <div className="form-group col-md-6">
                      <CustomTextInput
                        label="Numéro d'etudiant"
                        name="studentNumber"
                        isRequired
                        formProps={props}
                        placeholder="R144046058"
                      />
                    </div>
                  </div>

                  <CountryRegionCity
                    formikRef={formikRef}
                    regions={props.values.regions}
                    cities={props.values.cities}
                    loadRegionsToForm={saveRegions}
                    loadCitiesToForm={saveCities}
                    data={props.values}
                    fromEducation={false}
                    fromEdit
                    resetCity={resetCity}
                    onSelectCity={SelectCityHandler}
                    optionalName=""
                    error={props && props.errors && props.errors.region}
                    formProps={props}
                    errorCity={props && props.errors && props.errors.city}
                  >
                    {educationType == 'primaire' ||
                      educationType == 'préscolaire' ? (
                      <div className="col-md-5 ml-4 mt-4">
                        <label className="mr-2">Année scolaire réussie</label>
                        <Field
                          component={Switch}
                          type="checkbox"
                          name="succeed"
                        />
                      </div>
                    ) : (
                      <div className="col-md-6">
                        <Majors
                          onSelectMajor={SelectMajorHandler}
                          label={majorLabel}
                          name="major.id"
                          isRequired
                          isMeta
                          unique
                        />
                      </div>
                    )}
                  </CountryRegionCity>

                  {educationType != 'primaire' &&
                    educationType != 'préscolaire' ? (
                    <div className="form-row">
                      <div className="col-md-12 mb-3">
                        <label className="mr-2">Année scolaire réussie</label>
                        <Field
                          component={Switch}
                          type="checkbox"
                          name="succeed"
                        />
                      </div>
                    </div>
                  ) : null}

                  <div className="form-row">
                    <div className="form-group col-md-6">
                      <CustomTextInput
                        label="Note de fin d'année"
                        type="number"
                        name="mark"
                        isRequired
                        formProps={props}
                      ></CustomTextInput>
                    </div>
                    <div className="col-md-6">
                      <Honors
                        onSelectHonor={SelectHonorHandler}
                        label="Mention"
                        name="honor.id"
                        isRequired
                        isMeta
                        unique
                      ></Honors>
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group col-md-12">
                      <label className="mr-2">Semestre</label>
                      <div role="group" aria-labelledby="semestre-group" className="d-flex align-items-center">
                        <label className="mr-4" style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', padding: '8px 16px', border: '1px solid #007bff', borderRadius: '20px', background: '#f0f8ff', marginRight: '12px' }}>
                          <Field type="radio" name="semestre" value="semestre 1" style={{ marginRight: '8px' }} />
                          <span style={{ color: '#007bff', fontWeight: 500 }}>Semestre 1</span>
                        </label>
                        <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', padding: '8px 16px', border: '1px solid #28a745', borderRadius: '20px', background: '#f8fff0' }}>
                          <Field type="radio" name="semestre" value="semestre 2" style={{ marginRight: '8px' }} />
                          <span style={{ color: '#28a745', fontWeight: 500 }}>Semestre 2</span>
                        </label>
                      </div>
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group col-md-12">
                      <CustomTextArea
                        label="Commentaire"
                        name="comment"
                        formProps={props}
                        placeholder="Commentaire"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="d-flex justify-content-end  mt-4 mb-3">
              <button
                type="button"
                className="mx-2 px-3 btn-style outlined"
                onClick={handleClose}
              >
                Annuler
              </button>
              <button type="submit" className="mx-2 px-4 btn-style primary">
                {isSubmitting ? 'Loading...' : buttonText}
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
}
