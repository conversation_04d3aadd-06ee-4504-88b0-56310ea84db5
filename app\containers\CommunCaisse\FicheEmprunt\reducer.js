import produce from 'immer';
import {
  LOAD_EMPRUNT_DETAILS_ERROR,
  LOAD_EMPRUNT_DETAILS_SUCCESS,
  LOAD_EMPRUNT_DETAILS,
  DELETE_EMPRUNT,
  DELETE_EMPRUNT_SUCCESS,
  DELETE_EMPRUNT_ERROR,
  RESET_DELETE_EMPRUNT,
  RESET_ERROR,
} from './constants';

export const initialState = {
  loading: false,
  error: false,
  empruntDetails: false,
  deleteSuccess: false,
};

const ficheEmpruntReducer = (state = initialState, action) =>
  produce(state, draft => {
    switch (action.type) {
      case LOAD_EMPRUNT_DETAILS:
        draft.loading = true;
        draft.error = false;
        draft.empruntDetails = false;
        break;

      case LOAD_EMPRUNT_DETAILS_SUCCESS:
        draft.loading = false;
        draft.error = false;
        draft.empruntDetails = action.empruntDetails;
        break;

      case LOAD_EMPRUNT_DETAILS_ERROR:
        draft.loading = false;
        draft.error = action.error;
        draft.empruntDetails = false;
        break;

      case DELETE_EMPRUNT:
        draft.loading = true;
        draft.error = false;
        break;

      case DELETE_EMPRUNT_SUCCESS:
        draft.loading = false;
        draft.error = false;
        draft.deleteSuccess = true;
        break;

      case DELETE_EMPRUNT_ERROR:
        draft.loading = false;
        draft.error = action.error;
        draft.deleteSuccess = false;
        break;

      case RESET_DELETE_EMPRUNT:
        draft.deleteSuccess = false;
        break;

      case RESET_ERROR:
        draft.error = false;
        break;

      default:
        break;
    }
  });

export default ficheEmpruntReducer;
