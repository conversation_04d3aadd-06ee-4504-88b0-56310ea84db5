import {
  LOAD_CAISSES,
  LOAD_CAISSES_SUCCESS,
  LOAD_CAISSES_ERROR,
  ADD_CAISSE_REQUEST,
  ADD_CAISSE_SUCCESS,
  ADD_CAISSE_ERROR,
  UPDATE_CAISSE_REQUEST,
  UPDATE_CAISSE_SUCCESS,
  UPDATE_CAISSE_ERROR,
  DELETE_CAISSE,
  DELETE_CAISSE_SUCCESS,
  LOAD_CAISSE_BY_ID,
  LOAD_CAISSE_BY_ID_SUCCESS,
  LOAD_CAISSE_BY_ID_ERROR,
  RESET_ERROR,
} from './constants';

const initialState = {
  caisses: { content: [], totalPages: 0, totalElements: 0, pageSize: 10, number: 0 },
  currentCaisse: null,
  loading: false,
  error: null,
  deleteSuccess: false,
  success: false,
};

export default function communCaisseReducer(state = initialState, action) {
  switch (action.type) {
    case LOAD_CAISSES:
      return { ...state, loading: true, error: null };
    case LOAD_CAISSES_SUCCESS:
      return { ...state, loading: false, caisses: action.data };
    case LOAD_CAISSES_ERROR:
      return { ...state, loading: false, error: action.error };
    case ADD_CAISSE_REQUEST:
      return { ...state, loading: true, error: null, success: false };
    case ADD_CAISSE_SUCCESS:
      return { ...state, loading: false, success: true };
    case ADD_CAISSE_ERROR:
      return { ...state, loading: false, error: action.error };
    case UPDATE_CAISSE_REQUEST:
      return { ...state, loading: true, error: null, success: false };
    case UPDATE_CAISSE_SUCCESS:
      return { ...state, loading: false, success: true };
    case UPDATE_CAISSE_ERROR:
      return { ...state, loading: false, error: action.error };
    case DELETE_CAISSE:
      return { ...state, loading: true, error: null, deleteSuccess: false };
    case DELETE_CAISSE_SUCCESS:
      return { ...state, loading: false, deleteSuccess: true };
    case LOAD_CAISSE_BY_ID:
      return { ...state, loading: true, error: null, currentCaisse: null };
    case LOAD_CAISSE_BY_ID_SUCCESS:
      return { ...state, loading: false, currentCaisse: action.data };
    case LOAD_CAISSE_BY_ID_ERROR:
      return { ...state, loading: false, error: action.error };
    case RESET_ERROR:
      return { ...state, error: null, success: false, deleteSuccess: false, currentCaisse: null };
    default:
      return state;
  }
}
