import React, { useEffect, useState } from 'react';
import listStyles from 'Css/list.css';
import { useDispatch, useSelector } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import CustomPagination from 'components/Common/CustomPagination';

import btnStyles from 'Css/button.css';
import { createStructuredSelector } from 'reselect';
import { useHistory } from 'react-router-dom';
import DataTable from 'components/Common/DataTable';
import { Alert } from 'react-bootstrap';
import { makeSelectCaisses, makeSelectLoading, makeSelectError, makeSelectSuccess } from './selectors';
import communCaisseSaga from './saga';
import communCaisseReducer from './reducer';
import { loadCaisses, resetError } from './actions';
import CommunCaisseForm from './addCaisseEmprunt/CommunCaisseForm';
import { EDIT_ICON,VIEW_ICON } from 'components/Common/ListIcons/ListIcons';
import { COMMUN_CAISSE_LIST_KEY } from './constants';
import GenericFilter from 'containers/Common/Filter/GenericFilter';
import ExportIconSvg from 'images/icons/ExportIconSvg';

const key = COMMUN_CAISSE_LIST_KEY;

const stateSelector = createStructuredSelector({
    caisses: makeSelectCaisses,
    loading: makeSelectLoading,
    error: makeSelectError,
    success: makeSelectSuccess,
});

export default function CommunCaisseList(props) {
    useInjectReducer({ key, reducer: communCaisseReducer });
    useInjectSaga({ key, saga: communCaisseSaga });

    const {
        caisses,
        loading,
        error,
        success,
    } = useSelector(stateSelector);
    const dispatch = useDispatch();
    const history = useHistory();
    const [errorMessages, setErrorMessages] = useState('');
    const [activePage, setActivePage] = useState(1);
    const [itemsCountPerPage, setItemsCountPerPage] = useState(10);
    const [totalItemsCount, setTotalItemsCount] = useState(0);
    const [filterValues, setFilterValues] = useState({
        searchByDonorName: '',
        searchByStatus: '',
        searchByDateEmprunt: '',
        searchByDateRemboursement: '',
    });

    // Filter configs
    const principalInputsConfig = [
        {
            field: 'searchByDonorName',
            type: 'text',
            placeholder: 'Nom du donateur',
            label: 'Nom du Donateur',
        },
    ];
    const additionalInputsConfig = [
        {
            field: 'searchByDateEmprunt',
            type: 'date',
            placeholder: 'Date Emprunt',
            label: 'Date Emprunt',
        },
        {
            field: 'searchByDateRemboursement',
            type: 'date',
            placeholder: 'Date Remboursement',
            label: 'Date Remboursement',
        },
        {
            field: 'searchByStatus',
            type: 'select',
            placeholder: 'Statut',
            label: 'Statut',
            options: [
                { value: 'ACTIVE', label: 'Actif' },
                { value: 'COMPLETED', label: 'Terminé' },
                { value: 'CANCELLED', label: 'Annulé' },
            ]
        },
    ];

    // Data mapping for DataTable
    const rows = Array.isArray(caisses.content)
        ? caisses.content.map(caisse => ({
            id: caisse.id,
            donorId: caisse.donorId,
            donorName: caisse.donorName,
            globalAmount: caisse.globalAmount,
            remainingAmount: caisse.remainingAmount,
            totalEmprunt: caisse.totalEmprunt,
            totalRemboursement: caisse.totalRemboursement,
            lastDateEmprunt: caisse.lastDateEmprunt,
            lastDateRemboursement: caisse.lastDateRemboursement,
            status: caisse.status,
        }))
        : [];

    // Table columns
    const handleAdd = () => {
        history.push('/commun-caisse/add');
    };
    const handleEdit = (id) => {
        history.push(`/commun-caisse/edit/${id}`);
    };
    const columns = [
        {
            field: 'donorName',
            headerName: 'Donateur',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => (
                <span
                    onClick={() => history.push(`/donors/fiche/${params.row.donorId}/info`)}
                    style={{
                        color: '#4F89D7',
                        cursor: 'pointer',
                        textDecoration: 'underline'
                    }}
                    onMouseEnter={e => (e.currentTarget.style.color = '#0056b3')}
                    onMouseLeave={e => (e.currentTarget.style.color = '#4F89D7')}
                >
                    {params.value}
                </span>
            )
        },
        {
            field: 'globalAmount',
            headerName: 'Montant Global',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => (
                <span>{params.value ? `${params.value} DH` : '0 DH'}</span>
            )
        },
        {
            field: 'lastDateEmprunt',
            headerName: 'Dernière Date Emprunt',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => (
                <span>
                    {params.value ? new Date(params.value).toLocaleDateString('fr-FR') : '-'}
                </span>
            )
        },
        {
            field: 'lastDateRemboursement',
            headerName: 'Dernière Date Remboursement',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
            renderCell: params => (
                <span>
                    {params.value ? new Date(params.value).toLocaleDateString('fr-FR') : '-'}
                </span>
            )
        },
        {
            field: 'actions',
            headerName: 'Actions',
            flex: 1,
            headerAlign: 'center',
            align: 'center',
                renderCell: params => (
                           <div
                               style={{
                                   display: 'flex',
                                   flexDirection: 'row',
                                   justifyContent: 'center',
                                   alignItems: 'center',
                               }}
                           >
                               <input
                                   type="image"
                                   onClick={() => handleEdit(params.row.id)}
                                   className="p-2"
                                   src={EDIT_ICON}
                                   width="40px"
                                   height="40px"
                                   title="Modifier"
                                   style={{
                                       cursor: 'pointer'
                                   }}
                               />
                               <input
                                   type="image"
                                   onClick={() => handleEdit(params.row.id)}
                                   className="p-2"
                                   src={VIEW_ICON}
                                   width="40px"
                                   height="40px"
                                   title="Modifier"
                                   style={{
                                       cursor: 'pointer'
                                   }}
                               />
                           </div>
                       ),
        },
    ];

    // Filter apply handler
    const handleFilterApply = filters => {
        setFilterValues(filters);
        dispatch(loadCaisses(0, filters));
    };
    const handleResetFilterComplete = () => {
        setFilterValues({
            searchByDonorName: '',
            searchByStatus: '',
            searchByDateEmprunt: '',
            searchByDateRemboursement: ''
        });
        dispatch(loadCaisses(0, {}));
    };

    // Pagination
    const handlePageChange = pageNumber => {
        setActivePage(pageNumber);
        dispatch(loadCaisses(pageNumber - 1, filterValues));
    };


    useEffect(() => {
        // Load data on mount
        dispatch(loadCaisses(0, {})); // Load with empty filters on mount
        // Reset any existing success/error states when component mounts
        dispatch(resetError());
    }, [dispatch]);

    useEffect(() => {
        if (caisses) {
            setActivePage((caisses.number || 0) + 1);
            setItemsCountPerPage(caisses.pageSize || 10);
            setTotalItemsCount(caisses.totalElements || 0);
        }
    }, [caisses]);

    useEffect(() => {
        if (success) {
            // Only show success message if we're actually coming from a successful operation
            // Check if there's a navigation state or recent timestamp to determine if this is a fresh success
          
                setErrorMessages('La caisse emprunt est ajoutée avec succès !'); 
                setTimeout(() => {
                    setErrorMessages('');
                }, 3000);
            
            dispatch(loadCaisses(0, {})); // Reload with empty filters after success
            dispatch(resetError()); // Always reset the success state
        }
    }, [success, dispatch]);

    useEffect(() => {
        if (error) {
            const errorMessage = (error.response && error.response.data && error.response.data.detailedMessage) || error.message || 'Une erreur est survenue';
            setErrorMessages(errorMessage);
            setTimeout(() => setErrorMessages(''), 4000);
        }
    }, [error]);



    return (
        <div>
            {/* Alerts */}
            {errorMessages && (
                <Alert
                    variant={errorMessages.includes('succès') ? "success" : "danger"}
                    onClose={() => setErrorMessages('')}
                    dismissible
                >
                    {errorMessages}
                </Alert>
            )}
            <div className={listStyles.backgroundStyle}>
                {/* Filter Bar */}
                <GenericFilter
                    principalInputsConfig={principalInputsConfig}
                    additionalInputsConfig={additionalInputsConfig}
                    onApplyFilter={handleFilterApply}
                    setFilterValues={setFilterValues}
                    onResetFilterComplete={handleResetFilterComplete}
                    data={rows}
                />
                {/* Header with Add/Export */}
                <div className={listStyles.head} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <h4></h4>
                    <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '10px' }}>
                        <button className={`export-btn ${btnStyles.iconButton}`} disabled={rows.length === 0}>
                            <ExportIconSvg />
                        </button>
                        <button className={btnStyles.addBtnProfile} onClick={handleAdd}>
                            Ajouter une caisse
                        </button>
                    </div>
                </div>
                {/* Table */}
                <div className="sub-container">
                    <div className="table-container">
                        {loading ? (
                            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
                                <div className="spinner-border text-primary" role="status"></div>
                            </div>
                        ) : (
                            <>
                                <DataTable
                                    rows={rows}
                                    columns={columns}
                                    fileName={`Liste des  Caisse Emprunt, ${new Date().toLocaleString()}`}
                                />
                                {caisses.totalPages > 0 && (
                                    <div className="justify-content-center mt-3">
                                        <CustomPagination
                                            totalElements={totalItemsCount}
                                            totalCount={caisses.totalPages}
                                            pageSize={itemsCountPerPage}
                                            currentPage={activePage}
                                            onPageChange={handlePageChange}
                                        />
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                </div>
            </div>

        </div>
    );
}
