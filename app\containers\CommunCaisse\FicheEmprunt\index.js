import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';

import { Switch, Route, useParams, useHistory } from 'react-router-dom';
import btnStyles from '../../../Css/button.css';
import SideBar from 'components/CommunCaisse/FicheEmprunt/SideBar';
import HeaderEmprunt from 'components/CommunCaisse/Layout/HeaderEmprunt';
import EmpruntHistorique from 'components/CommunCaisse/FicheEmprunt/EmpruntHistorique';
import { Alert, Modal, Button } from 'react-bootstrap';
import { CircularProgress } from '@material-ui/core';
import styles from '../../../Css/profileList.css';
import { loadEmpruntDetails, resetDeleteEmprunt, resetError } from './actions';
import {
  makeSelectEmpruntDetails,
  makeSelectDeleteSuccess,
  makeSelectError,
  makeSelectLoading,
} from './selectors';
import ficheEmpruntReducer from './reducer';
import detailEmpruntSaga from './saga';

const key = 'ficheEmprunt';

const omdbSelector = createStructuredSelector({
  loading: makeSelectLoading,
  empruntDetails: makeSelectEmpruntDetails,
  deleteSuccess: makeSelectDeleteSuccess,
  error: makeSelectError,
});

export default function FicheEmprunt() {
  const params = useParams();
  const history = useHistory();

  useInjectReducer({ key, reducer: ficheEmpruntReducer });
  useInjectSaga({ key, saga: detailEmpruntSaga });

  const { loading, empruntDetails, deleteSuccess, error } = useSelector(
    omdbSelector,
  );

  const [errorMessages, setErrorMessages] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [empruntToDelete, setEmpruntToDelete] = useState(null);
  const [showAlert, setShowAlert] = useState(false);

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(loadEmpruntDetails({ id: params.id }));
  }, []);

  useEffect(() => {
    if (deleteSuccess) {
      setSuccessMessage('Emprunt supprimé avec succès !');
      setShowAlert(true);
      setTimeout(() => {
        setShowAlert(false);
        dispatch(resetDeleteEmprunt());
        history.push('/commun-caisse');
      }, 3000);
    }
  }, [deleteSuccess]);

  useEffect(() => {
    if (error) {
      setErrorMessages('Une erreur est survenue lors du chargement des détails de l\'emprunt.');
      setShowAlert(true);
      setTimeout(() => {
        setShowAlert(false);
        dispatch(resetError());
      }, 3000);
    }
  }, [error]);

  const handleDelete = () => {
    setShowModal(true);
    setEmpruntToDelete(empruntDetails);
  };

  const handleQuit = () => {
    history.push('/commun-caisse');
  };

  const confirmDelete = () => {
    if (empruntToDelete) {
      // dispatch(deleteEmprunt(empruntToDelete.id));
      setShowModal(false);
      setEmpruntToDelete(null);
    }
  };

  const cancelDelete = () => {
    setShowModal(false);
    setEmpruntToDelete(null);
  };

  let successAlert = null;
  let errorAlert = null;

  if (showAlert && successMessage) {
    successAlert = (
      <Alert variant="success" onClose={() => setShowAlert(false)} dismissible>
        {successMessage}
      </Alert>
    );
  }

  if (showAlert && errorMessages) {
    errorAlert = (
      <Alert variant="danger" onClose={() => setShowAlert(false)} dismissible>
        {errorMessages}
      </Alert>
    );
  }

  return (
    <>
      {successAlert}
      {errorAlert}

      {/* Loading Modal */}
      <Modal show={loading} centered contentClassName="bg-transparent border-0">
        <div className="d-flex justify-content-center align-items-center">
          <CircularProgress style={{ width: '100px', height: '100px' }} />
        </div>
      </Modal>
      
      <HeaderEmprunt isNature={empruntDetails} />
      <div className="row">
        <div className="col-3 pr-0 pl-2">
          <SideBar data={empruntDetails} />
          <div className="d-flex flex-column align-items-center justify-content-center gap-10">
            <button
              onClick={handleQuit}
              className={`btn-style secondary w-100`}
            >
              Quitter
            </button>
          </div>
        </div>

        <div className="col-9 pr-2">
          <div className={styles.backgroudStyle}>
            <Switch>
              <Route exact path="/commun-caisse/fiche/:id/historique">
                <EmpruntHistorique data={empruntDetails} />
              </Route>
            </Switch>
          </div>
        </div>
      </div>
    </>
  );
}
