import React, { useEffect } from 'react';
import { createStructuredSelector } from 'reselect';
import { useSelector, useDispatch } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';

import saga from './saga';
import reducer from './reducer';
import { loadFamilyRelationships } from './actions';
import { makeSelectFamilyRelationships } from './selectors';
import { CustomSelect } from '../../CustomInputs/CustomSelect';

const key = 'familyRelationship';

const omdbSelector = createStructuredSelector({
  FamilyRelationships: makeSelectFamilyRelationships,
});

export default function FamilyRelationships(props) {
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  const { FamilyRelationships } = useSelector(omdbSelector);

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(loadFamilyRelationships('search'));
  }, []);

  let familyRelationships = null;

  if (FamilyRelationships) {
    familyRelationships = FamilyRelationships.filter(familyRelationship=>{
      return familyRelationship.id!=1 && familyRelationship.id!=2
    }).map(familyRelationship => {
      return (
        <option key={familyRelationship.id} value={familyRelationship.id}>
          {' '}
          {familyRelationship.name}{' '}
        </option>
      );
    });
  
  }

  return (
    <CustomSelect {...props}
    isRequired={props.isRequired}
      isMeta = {props.isMeta}
      unique= {props.unique}
    >
      <option value="">-- Lien de parenté --</option>
      {familyRelationships}
    </CustomSelect>
  );
}
