import React from 'react';
import { Link } from 'react-router-dom';
import sideBarStyles from '../../../../Css/sideBar.css';
import { useHistory } from 'react-router-dom';
import tagStyles from '../../../../Css/tag.css';

const SideBar = props => {
  const emprunt = props.data;
  const history = useHistory();

  const getStatusTag = () => {
    if (!emprunt || !emprunt.status) return tagStyles.tagRed;


  };


  const formatDate = (dateString) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
  };

  const formatAmount = (amount) => {
    if (!amount && amount !== 0) return '0';
    return amount.toLocaleString('fr-FR');
  };

  const getAmountBadgeStyle = (type, amount) => {
    const baseStyle = {
      padding: '4px 12px',
      borderRadius: '4px',
      color: 'white',
      fontWeight: 'bold',
      fontSize: '0.9rem',
      display: 'inline-block',
      minWidth: '80px',
      textAlign: 'center'
    };

    if (type === 'disponible') {
      return { ...baseStyle, backgroundColor: '#28a745' }; // Green
    } else if (type === 'reserve') {
      return { ...baseStyle, backgroundColor: '#ffc107', color: '#000' }; // Yellow/Orange
    } else if (type === 'execute') {
      return { ...baseStyle, backgroundColor: '#dc3545' }; // Red
    }
    return baseStyle;
  };

  let top = null;
  let text = null;

  if (emprunt) {
    top = (
      <div className={sideBarStyles.topCard}>
        <div className={sideBarStyles.top}>
          <span className={sideBarStyles.value}>
            {emprunt.donorName ? (
              <Link
                to={`/donors/fiche/${emprunt.donorId}/info`}
                style={{ color: "blue", textDecoration: "underline", cursor: "pointer", fontSize: "0.9rem" }}
              >
                {emprunt.donorName}
              </Link>
            ) : '-'}
          </span>
        </div>
      </div>
    );

    text = (
      <div>
        <p>
          <span className={sideBarStyles.value}>{formatAmount(emprunt.globalAmount)} DH</span>
        </p>
        <div style={{ marginBottom: '10px' }}>
          <span style={getAmountBadgeStyle('disponible')}>
            {formatAmount(emprunt.remainingAmount)} DH
          </span>
        </div>
        <div style={{ marginBottom: '10px' }}>
          <span style={getAmountBadgeStyle('reserve')}>
            {formatAmount(emprunt.totalEmprunt)} DH
          </span>
        </div>
        <div style={{ marginBottom: '10px' }}>
          <span style={getAmountBadgeStyle('execute')}>
            {formatAmount(emprunt.totalRemboursement)} DH
          </span>
        </div>
        <p>
          <span className={sideBarStyles.value}>{formatDate(emprunt.lastDateEmprunt)}</span>
        </p>
        <p>
          <span className={sideBarStyles.value}>{formatDate(emprunt.lastDateRemboursement)}</span>
        </p>
      </div>
    );
  }

  return (
    <>
      <div className={sideBarStyles.sideBar}>
        {top}
        <div className={`d-flex justify-content-center gap-30 mt-5`}>
          <div>
            <p>Montant Global :</p>
            <p>Total disponible :</p>
            <p>Total Réservé :</p>
            <p>Total Exécuté :</p>
            <p>Dernière Date Emprunt :</p>
            <p>Dernière Date Remboursement :</p>
          </div>
          {text}
        </div>
      </div>
    </>
  );
};

export default SideBar;
