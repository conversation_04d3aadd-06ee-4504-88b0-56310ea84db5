import React from 'react';
import { Link } from 'react-router-dom';
import sideBarStyles from '../../../../Css/sideBar.css';
import { useHistory } from 'react-router-dom';
import tagStyles from '../../../../Css/tag.css';

const SideBar = props => {
  const emprunt = props.data;
  const history = useHistory();

  const getStatusTag = () => {
    if (!emprunt || !emprunt.status) return tagStyles.tagRed;


  };


  const formatDate = (dateString) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
  };

  const formatAmount = (amount) => {
    if (!amount && amount !== 0) return '-';
    return `${amount.toLocaleString('fr-FR')} DH`;
  };

  let top = null;
  let text = null;

  if (emprunt) {
    top = (
      <div className={sideBarStyles.topCard}>
        <div className={sideBarStyles.top}>
          <span className={sideBarStyles.value}>
            {emprunt.donorName ? (
              <Link
                to={`/donors/fiche/${emprunt.donorId}/info`}
                style={{ color: "blue", textDecoration: "underline", cursor: "pointer", fontSize: "0.9rem" }}
              >
                {emprunt.donorName}
              </Link>
            ) : '-'}
          </span>
        </div>
      </div>
    );

    text = (
      <div>
        <p>
          <span className={sideBarStyles.value}>{formatAmount(emprunt.globalAmount)}</span>
        </p>
        <p>
          <span className={sideBarStyles.value}>{formatAmount(emprunt.remainingAmount)}</span>
        </p>
        <p>
          <span className={sideBarStyles.value}>{formatAmount(emprunt.totalEmprunt)}</span>
        </p>
        <p>
          <span className={sideBarStyles.value}>{formatAmount(emprunt.totalRemboursement)}</span>
        </p>
        <p>
          <span className={sideBarStyles.value}>{formatDate(emprunt.lastDateEmprunt)}</span>
        </p>
        <p>
          <span className={sideBarStyles.value}>{formatDate(emprunt.lastDateRemboursement)}</span>
        </p>
      </div>
    );
  }

  return (
    <>
      <div className={sideBarStyles.sideBar}>
        {top}
        <div className={`d-flex justify-content-center gap-30 mt-5`}>
          <div>  
            <p>Montant Global :</p>
            <p>Montant Restant :</p>
            <p>Total Emprunt :</p>
            <p>Total Remboursement :</p>
            <p>Dernière Date Emprunt :</p>
            <p>Dernière Date Remboursement :</p> 
          </div>
          {text}
        </div>
      </div>
    </>
  );
};

export default SideBar;
